-- Enable the TimescaleDB extension (run once per database)
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Reference data for exchanges
CREATE TABLE exchanges (
    exchange_code VARCHAR(10) PRIMARY KEY,
    exchange_name VARCHAR(100) NOT NULL,
    country CHAR(2) DEFAULT 'US',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert common exchanges
INSERT INTO exchanges (exchange_code, exchange_name) VALUES
('NASDAQ', 'NASDAQ Stock Market'),
('NYSE', 'New York Stock Exchange'),
('AMEX', 'American Stock Exchange'),
('OTC', 'Over-the-Counter'),
('BATS', 'BATS Global Markets'),
('IEX', 'Investors Exchange');

CREATE TABLE sic_codes (
    sic_code VARCHAR(4) PRIMARY KEY,
    office_title VARCHAR(255) NOT NULL,
    industry_title VARCHAR(255) NOT NULL,
    division VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Data quality constraints
    CONSTRAINT valid_sic_code CHECK (sic_code ~ '^\d{4}$')
);

CREATE TABLE companies (
    cik VARCHAR(10) PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    ticker_symbol VARCHAR(6),
    exchange VARCHAR(10),
    ein VARCHAR(20),
    fiscal_year_end CHAR(4),
    company_start_date DATE,
    incorporation_state CHAR(2),
    company_state CHAR(2),
    sic_code VARCHAR(4),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Data quality constraints
    CONSTRAINT valid_cik CHECK (cik ~ '^\d{10}$'),
    CONSTRAINT valid_ticker CHECK (ticker_symbol IS NULL OR ticker_symbol ~ '^[A-Z]{1,6}$'),
    
    -- Foreign key constraints
    CONSTRAINT fk_companies_sic_code 
        FOREIGN KEY (sic_code) REFERENCES sic_codes(sic_code) ON DELETE SET NULL,
    CONSTRAINT fk_companies_exchange 
        FOREIGN KEY (exchange) REFERENCES exchanges(exchange_code) ON DELETE SET NULL,
    CONSTRAINT unique_ticker_when_not_null 
        UNIQUE (ticker_symbol) DEFERRABLE INITIALLY DEFERRED
);

CREATE TABLE filings (
    id BIGSERIAL PRIMARY KEY,
    accession_number VARCHAR(25) NOT NULL,
    conformed_submission_type VARCHAR(20) NOT NULL,
    public_document_count SMALLINT NOT NULL DEFAULT 0,
    conformed_period_of_report DATE,
    filed_as_of_date DATE NOT NULL,
    date_as_of_change DATE,
    company_conformed_name VARCHAR(255) NOT NULL,
    company_cik VARCHAR(10) NOT NULL,
    processing_status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    retry_count SMALLINT DEFAULT 0,
    processed_timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Data quality constraints
    CONSTRAINT valid_processing_status CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    CONSTRAINT check_public_document_count CHECK (public_document_count >= 0),
    CONSTRAINT check_filing_dates CHECK (filed_as_of_date >= conformed_period_of_report OR conformed_period_of_report IS NULL),
    CONSTRAINT check_retry_count CHECK (retry_count >= 0),
    
    -- Foreign key constraints
    CONSTRAINT fk_filings_company_cik 
        FOREIGN KEY (company_cik) REFERENCES companies(cik) ON DELETE CASCADE,
    CONSTRAINT unique_accession_number 
        UNIQUE (accession_number)
);

-- Convert filings into a hypertable on filed_as_of_date (better partitioning strategy)
SELECT create_hypertable('filings', 'filed_as_of_date', chunk_time_interval => interval '1 year');

CREATE TABLE filing_documents (
  id BIGSERIAL PRIMARY KEY,
  filing_id BIGINT NOT NULL,
  filed_as_of_date DATE NOT NULL,
  document_type VARCHAR(64) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  description TEXT,
  contains_html BOOLEAN NOT NULL DEFAULT FALSE,
  document_text TEXT,
  file_size_bytes BIGINT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

  -- Data quality constraints
  CONSTRAINT check_file_size CHECK (file_size_bytes IS NULL OR file_size_bytes >= 0),
  
  -- Foreign key constraints
  CONSTRAINT fk_filing_documents_filing_id
    FOREIGN KEY (filing_id) REFERENCES filings(id) ON DELETE CASCADE,
  CONSTRAINT unique_filing_filename
    UNIQUE (filing_id, filename)
);

-- Indexes for performance on companies
CREATE INDEX idx_companies_sic_code ON companies(sic_code);
CREATE INDEX idx_companies_exchange ON companies(exchange);

-- Partial indexes for active companies only (performance improvement)
CREATE INDEX idx_companies_active_ticker ON companies(ticker_symbol) 
    WHERE is_active = TRUE AND ticker_symbol IS NOT NULL;
CREATE INDEX idx_companies_active_sic ON companies(sic_code) 
    WHERE is_active = TRUE;

-- Indexes for filings (now the hypertable)
CREATE INDEX idx_filings_accession_number ON filings(accession_number);
CREATE INDEX idx_filings_company_cik ON filings(company_cik);
CREATE INDEX idx_filings_submission_type ON filings(conformed_submission_type);
CREATE INDEX idx_filings_period_report ON filings(conformed_period_of_report);
CREATE INDEX idx_filings_processing_status ON filings(processing_status);

-- Composite indexes for common query patterns on filings
CREATE INDEX idx_filings_company_type_date ON filings(company_cik, conformed_submission_type, filed_as_of_date);
CREATE INDEX idx_filings_status_date ON filings(processing_status, filed_as_of_date);

-- Indexes for filing_documents
CREATE INDEX idx_filing_documents_filing_id ON filing_documents(filing_id);
CREATE INDEX idx_filing_documents_type ON filing_documents(document_type);
CREATE INDEX idx_filing_documents_html ON filing_documents(contains_html);
CREATE INDEX idx_filing_documents_filing_id_type ON filing_documents(filing_id, document_type);

-- Full-text search index for document content
CREATE INDEX idx_filing_documents_text_search ON filing_documents 
    USING gin(to_tsvector('english', document_text))
    WHERE document_text IS NOT NULL;

-- Composite indexes for companies
CREATE INDEX idx_companies_state_sic ON companies(incorporation_state, sic_code);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_exchanges_updated_at 
    BEFORE UPDATE ON exchanges 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sic_codes_updated_at 
    BEFORE UPDATE ON sic_codes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at 
    BEFORE UPDATE ON companies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE FUNCTION inherit_filed_as_of_date()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.filed_as_of_date IS NULL THEN
    SELECT filed_as_of_date INTO NEW.filed_as_of_date
    FROM filings WHERE id = NEW.filing_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_inherit_filed_as_of_date
BEFORE INSERT ON filing_documents
FOR EACH ROW EXECUTE FUNCTION inherit_filed_as_of_date();

-- Set compression for large text fields (performance improvement)
ALTER TABLE filing_documents SET (toast_tuple_target = 8160);

-- Comments for documentation
COMMENT ON TABLE exchanges IS 'Reference data for stock exchanges';
COMMENT ON TABLE sic_codes IS 'Standard Industrial Classification codes for categorizing companies by industry';
COMMENT ON TABLE companies IS 'Core company information from SEC filings';
COMMENT ON TABLE filings IS 'SEC filing metadata and header information (hypertable partitioned by filed_as_of_date)';
COMMENT ON TABLE filing_documents IS 'Individual documents within each SEC filing';

COMMENT ON COLUMN companies.cik IS 'Central Index Key - unique identifier assigned by SEC (10 digits)';
COMMENT ON COLUMN companies.ein IS 'Employer Identification Number for tax purposes';
COMMENT ON COLUMN companies.ticker_symbol IS 'Stock ticker symbol (1-6 uppercase letters)';
COMMENT ON COLUMN filings.accession_number IS 'Unique identifier for each SEC filing';
COMMENT ON COLUMN filings.processing_status IS 'Current processing status of the filing';
COMMENT ON COLUMN filings.retry_count IS 'Number of processing retry attempts';
COMMENT ON COLUMN filing_documents.document_text IS 'Full text content of the document (consider external storage for large files)';
